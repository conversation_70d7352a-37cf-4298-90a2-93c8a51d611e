# CompatibilityFrame Implementation Guide

## Overview

The `CompatibilityFrame` control is a custom implementation that provides backward compatibility with the deprecated `Frame` control in .NET 9+. It maintains the same API as the original Frame control while internally using the new `Border` control for rendering.

## Background

Microsoft deprecated the `Frame` control in .NET 9 in favor of the more versatile `Border` control. However, this change breaks existing XAML code that relies on Frame controls. The `CompatibilityFrame` provides a drop-in replacement that allows existing Frame-based XAML to continue working without modification.

## Implementation Details

### Class Structure

```csharp
public class CompatibilityFrame : Border
```

The `CompatibilityFrame` inherits from `Border` and provides Frame-compatible properties that map to corresponding Border properties.

### Key Properties

| Frame Property | CompatibilityFrame Property | Border Mapping |
|----------------|----------------------------|----------------|
| `BackgroundColor` | `BackgroundColor` | `Background` (as SolidColorBrush) |
| `BorderColor` | `BorderColor` | `Stroke` (as SolidColorBrush) |
| `CornerRadius` | `CornerRadius` | `StrokeShape` (as RoundRectangle) |
| `HasShadow` | `HasShadow` | `Shadow` |
| `BorderWidth` | `BorderWidth` | `StrokeThickness` |

### Property Mapping Logic

1. **BackgroundColor**: Converts Color to SolidColorBrush and sets Border.Background
2. **BorderColor**: Converts Color to SolidColorBrush and sets Border.Stroke
3. **CornerRadius**: Creates RoundRectangle with specified radius and sets Border.StrokeShape
4. **HasShadow**: Creates or removes Shadow object on Border
5. **BorderWidth**: Maps directly to Border.StrokeThickness

## Usage

### Basic Usage

Replace existing Frame controls with CompatibilityFrame:

```xml
<!-- Before (deprecated Frame) -->
<Frame BackgroundColor="White" 
       BorderColor="Gray" 
       CornerRadius="10" 
       Padding="15">
    <Label Text="Content" />
</Frame>

<!-- After (CompatibilityFrame) -->
<controls:CompatibilityFrame BackgroundColor="White" 
                            BorderColor="Gray" 
                            CornerRadius="10" 
                            Padding="15">
    <Label Text="Content" />
</controls:CompatibilityFrame>
```

### XAML Namespace

Add the controls namespace to your XAML:

```xml
xmlns:controls="clr-namespace:MauiApp10PushNotifications.Pages.Controls"
```

### Theme Binding Support

CompatibilityFrame supports theme bindings just like the original Frame:

```xml
<controls:CompatibilityFrame BackgroundColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray900}}"
                            BorderColor="Transparent"
                            CornerRadius="10"
                            Padding="15">
    <Label Text="Themed Content" />
</controls:CompatibilityFrame>
```

## Available Styles

The implementation includes several predefined styles:

### Default Style

```xml
<Style TargetType="controls:CompatibilityFrame">
    <Setter Property="BackgroundColor" Value="{AppThemeBinding Light={StaticResource White}, Dark={StaticResource Gray900}}"/>
    <Setter Property="BorderColor" Value="Transparent"/>
    <Setter Property="CornerRadius" Value="0"/>
    <Setter Property="HasShadow" Value="False"/>
    <Setter Property="BorderWidth" Value="0"/>
    <Setter Property="Padding" Value="10"/>
</Style>
```

### Card Style

```xml
<controls:CompatibilityFrame Style="{StaticResource FrameCardStyle}">
    <Label Text="Card Content" />
</controls:CompatibilityFrame>
```

### Elevated Style (with shadow)

```xml
<controls:CompatibilityFrame Style="{StaticResource ElevatedFrameStyle}">
    <Label Text="Elevated Content" />
</controls:CompatibilityFrame>
```

### Outlined Style (with border)

```xml
<controls:CompatibilityFrame Style="{StaticResource OutlinedFrameStyle}">
    <Label Text="Outlined Content" />
</controls:CompatibilityFrame>
```

## Migration Guide

### Step 1: Add Namespace

Add the controls namespace to your XAML files:

```xml
xmlns:controls="clr-namespace:MauiApp10PushNotifications.Pages.Controls"
```

### Step 2: Replace Frame Tags

Replace all `<Frame>` tags with `<controls:CompatibilityFrame>`:

```xml
<!-- Find and replace -->
<Frame → <controls:CompatibilityFrame
</Frame> → </controls:CompatibilityFrame>
```

### Step 3: Test and Validate

Build and test your application to ensure all Frame controls render correctly.

## Platform Considerations

- **Shadow Support**: Shadow rendering may vary by platform. The implementation provides basic shadow support that works across all platforms.
- **Performance**: CompatibilityFrame has minimal performance overhead compared to the original Frame control.
- **Styling**: All existing Frame styles can be adapted to work with CompatibilityFrame.

## Limitations

1. **Shadow Customization**: Shadow properties are simplified compared to platform-specific implementations.
2. **Advanced Border Features**: Some advanced Border features are not exposed through the Frame-compatible API.

## Future Considerations

- Consider migrating to native Border controls when feasible for new development.
- CompatibilityFrame should be viewed as a transitional solution to maintain backward compatibility.
- Monitor Microsoft's recommendations for Frame control alternatives in future .NET releases.

## Technical Notes

- Uses `new` keyword to hide inherited BackgroundColor property from VisualElement
- Implements proper property change handlers for all Frame-compatible properties
- Supports data binding and theme binding
- Compatible with existing Frame-based styles with minimal modifications
